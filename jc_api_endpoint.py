import psycopg2
import psycopg2.extras
import requests
from datetime import datetime
from pytz import timezone

# Dizionario con le possibili operazioni di trasformazione
operationDict = {"0": "JOIN", "1": "FILTER", "2": "SPATIALJOIN"}

# Dizionario con le possibili tipologie di job
jobTypeDict = {"0": "INGESTION", "1": "TRANSFORMATION", "2": "LOAD"}

ingestionTrackingQuery = "insert into public.ingestion_jobs (datasource_name,job_name,input_table,output_table,tstamp) VALUES (%s, %s,%s,%s,%s) ON CONFLICT ON CONSTRAINT ingestion_jobs_pk DO UPDATE SET (datasource_name, job_name, tstamp) = (EXCLUDED.datasource_name, EXCLUDED.job_name, EXCLUDED.tstamp);"

transformationTrackingQuery = "insert into public.transform_jobs (job_name,input_table,output_table,tstamp,step_count,operation) VALUES (%s, %s,%s,%s,%s, %s) ON CONFLICT ON CONSTRAINT transform_jobs_pk DO UPDATE SET (job_name, tstamp) = (EXCLUDED.job_name, EXCLUDED.tstamp);"

loadTrackingQuery = "insert into public.load_jobs (job_name,staging_table,centraldb_table,tstamp) VALUES (%s, %s,%s,%s) ON CONFLICT ON CONSTRAINT load_jobs_pk DO UPDATE SET (job_name, tstamp) = (EXCLUDED.job_name, EXCLUDED.tstamp);"

def connectDb(user, password, host, port, dbname):
    """Funzione che effettua la connessione ad un database. """

    try:
        ocon = psycopg2.connect(user=user,
                                password=password,
                                host=host,
                                port=port,
                                dbname=dbname
                                )
        print("Connessione Riuscita ")
        ocon.set_client_encoding('LATIN1')
    except:
        print("Connessione non riuscita")
        raise
    else:
        return ocon


def tracking(ocon, trackingQuery, **kwargs):
    """Funzione che inserisce un recod nella tabella ingestion_jobs, transform_jobs o load_jobs del database idumanager. """

    parameterlist = []
    cur = ocon.cursor()

    for arg in kwargs.values():
        parameterlist.append(arg)

    # print(trackingQuery)
    # print(parameterlist)
    cur.execute(trackingQuery, parameterlist)
    cur.close()
    ocon.commit()


def insertResourceLastModified(conn, output_table, output_schema, schema_idu_mngr, lastModified = None):

    with conn.cursor() as curr:

        if lastModified is not None:
            strrr = "INSERT INTO public.resource_last_modified (resource_name, last_modified) VALUES('"+output_schema+"."+output_table+"', '"+lastModified+"') ON CONFLICT ON CONSTRAINT resource_last_modified_pk DO UPDATE set last_modified = '"+lastModified+"';"
        else:
            strrr = "INSERT INTO public.resource_last_modified (resource_name, last_modified) VALUES('"+output_schema+"."+output_table+"', '"+str(datetime.now().astimezone(timezone('Europe/Rome')))+"') ON CONFLICT ON CONSTRAINT resource_last_modified_pk DO UPDATE set last_modified = '"+str(datetime.now().astimezone(timezone('Europe/Rome')))+"';"
        curr.execute(strrr)
        conn.commit()

def createApiResponseTable(conn):
    """Crea la tabella api_response se non esiste."""
    create_table_query = """
    CREATE TABLE IF NOT EXISTS public.api_response (
        id SERIAL PRIMARY KEY,
        api_endpoint TEXT NOT NULL,
        response_data JSONB,
        fetched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    with conn.cursor() as cur:
        cur.execute(create_table_query)
        conn.commit()
    print("Tabella api_response verificata/creata")

def ingestFromApi(api_url, conn):
    """Esegue la chiamata API e persiste la risposta nella tabella api_response."""

    try:
        # Chiama l'API
        print(f"Chiamata API: {api_url}")
        response = requests.get(api_url, timeout=30)
        response.raise_for_status()  # Solleva eccezione per status code di errore

        # Prepara i dati per l'inserimento
        response_data = response.json() if response.headers.get('content-type', '').startswith('application/json') else {'text': response.text}

        # Inserisci nella tabella
        insert_query = """
        INSERT INTO public.api_response (api_endpoint, response_data)
        VALUES (%s, %s);
        """

        with conn.cursor() as cur:
            cur.execute(insert_query, (api_url, psycopg2.extras.Json(response_data)))
            conn.commit()

        print(f"Risposta API salvata nella tabella api_response. Status: {response.status_code}")

    except requests.RequestException as e:
        print(f"Errore nella chiamata API: {e}")
        raise
    except Exception as e:
        print(f"Errore durante il salvataggio della risposta: {e}")
        raise


def main(in_out_tablelist, dbuser, dbpassword, dbhost, dbport, dbname, jobType, job_name, datasource_name = None, step_count = None, operation = None, dbuser_tracking=None, dbpassword_tracking=None, dbhost_tracking=None, dbport_tracking=None, dbname_tracking=None):
    """
    Funzione che effettua un controllo sugli input e in base ad essi chiama una delle tre funzioni che effettuano il tracking in base al tipo di job. Se stiamo effettuando un job di TRANSFORMATION, questa funzione bisogna richiamarla ad ogni step_count, specificando il tipo di operation.

    Parametri di input:

    in_out_tablelist: lista di tuple. ogni tupla deve avere due elementi. Il primo che indica il nome della tabella di input, il secondo che indica
                      il nome della tabella di output.
                      Esempio: in_out_tablelist = [("InTable4", "OutTable4"), ("InTable5", "OutTable5"), ("InTable6", "OutTable6")]
    dbuser: nome utente del db operativo (postgres)
    dbpassword: password associata all'utente del db operativo col quale bisogna connettersi (Idudb001!)
    dbhost: host del db operativo (idu-postgres-01.c84h5k4zmxdn.eu-west-1.rds.amazonaws.com)
    dbport: porta (5432)
    dbname: nome del db operativo (idumanager)
    jobType: tipologia di job (possibili valori: INGESTION, TRANSFORMATION, LOAD)
    job_name: nome del job custom
    datasource_name: nome della sorgente dalla quale si vogliono ingestionare dati nella staging area. Questo parametro di default Ã¨ impostato a None. Bisogna
                        utilizzarlo solo se il jobType Ã¨ di tipo INGESTION.
                        N.B.: il datasource_name deve corrispondere al nome di una sorgente configurata sull'IDU Manager
    step_count: intero che indica lo step di transformation. Questo parametro di default Ã¨ impostato a None. Bisogna utilizzarlo solo se il jobType Ã¨ di
                tipo TRANSFORMATION
    operation: indica il tipo di operazione di trasformazione (possibili valori: FILTER, JOIN, SPATIALJOIN). Questo parametro di default Ã¨ impostato a
                None. Bisogna utilizzarlo solo se il jobType Ã¨ di tipo TRANSFORMATION
    dbuser_tracking: nome utente del db di tracking (postgres)
    dbpassword_tracking: password associata all'utente del db di tracking
    dbhost_tracking: host del db di tracking
    dbport_tracking: porta del db di tracking (5432)
    dbname_tracking: nome del db di tracking (idumanager)
    """

    ocon = connectDb(dbuser, dbpassword, dbhost, dbport, dbname)
    ocon_tracking = None
    if dbuser_tracking and dbpassword_tracking and dbhost_tracking and dbport_tracking and dbname_tracking:
        ocon_tracking = connectDb(dbuser_tracking, dbpassword_tracking, dbhost_tracking, dbport_tracking, dbname_tracking)
    else:
        ocon_tracking = ocon  # fallback to same db
    tstamp = datetime.now()

    # Per job di INGESTION da API, esegui prima l'ingestion
    if jobType == jobTypeDict["0"] and datasource_name == "JSONPlaceholder API":
        # Crea la tabella se necessario
        createApiResponseTable(ocon)

        # Esegui l'ingestion per ogni input (API endpoint)
        for input_table, _ in in_out_tablelist:
            if input_table.startswith("http"):
                ingestFromApi(input_table, ocon)

    # Lista che conterrà dizionari con coppie di input_table ed output_table
    TrackingTableList = [] # [{"input_table": InTable1, "output_table": OutTable1}, {"input_table": InTable2, "output_table": OutTable2}, {"input_table": InTable3, "output_table": OutTable3}]

    for el in in_out_tablelist:
        # Si creano tanti dizionari per ogni tupla presente nella lista di input
        TrackingTableList.append({"input_table": el[0], "output_table": el[1]})

    # jobType può avere solo i seguenti valori: INGESTION, TRANSFORMATION o LOAD
    if jobType == jobTypeDict["0"] and datasource_name != None:
        for el in TrackingTableList:
            tracking(ocon_tracking, ingestionTrackingQuery, datasource_name = datasource_name, job_name = job_name, inp = el["input_table"], out = el["output_table"], tstamp = tstamp)

    # step_count deve essere un intero
    # operation puÃ² avere solo i seguenti valori: FILTER, JOIN o SPATIALJOIN
    elif jobType == jobTypeDict["1"] and step_count != None and operation != None and operation in operationDict.values():
        for el in TrackingTableList:
            tracking(ocon_tracking, transformationTrackingQuery, job_name = job_name, inp = el["input_table"], out = el["output_table"], tstamp = tstamp, step_count = step_count, operation = operation)

    elif jobType == jobTypeDict["2"]:
        for el in TrackingTableList:
            tracking(ocon_tracking, loadTrackingQuery, job_name = job_name, inp = el["input_table"], out = el["output_table"], tstamp = tstamp)

    else:
        print("Errore nell'impostazione degli input")

    for el in in_out_tablelist:
        tableNameList = el[1].split(".")
        outputSchema = tableNameList[0]
        outputTableName = tableNameList[1]
        insertResourceLastModified(ocon_tracking, outputTableName, outputSchema, "public", lastModified = str(datetime.now().astimezone(timezone('Europe/Rome'))))


"""Parametri di input"""

# Lista di tuple. Ognuna è composta da due elementi: il primo indica il nome dell'endpoint API di input e il secondo il nome della tabella di output
InOutTableList = [("https://jsonplaceholder.typicode.com/posts/1", "public.api_response")]

in_out_tablelist = InOutTableList
dbuser = "postgres"
dbpassword = "Idudb001!"
dbhost = "stage-idu-postgresql.czjrp3ja7e6q.eu-south-1.rds.amazonaws.com"
dbport = "5432"
dbname = "stagingarea"
jobType = "INGESTION"  # INGESTION, TRANSFORMATION o LOAD
job_name = "jc_api_endpoint"
datasource_name = "JSONPlaceholder API" # Nome specifico per attivare l'ingestion API
step_count = None # None di default. Un intero che indica lo step di transformazion (0 per il primo step, 1 per il secondo ecc.)
operation = None # None di default. FILTER, JOIN o SPATIALJOIN

# Parametri per il database di tracking (stesso host, user, password, port del database di staging, solo dbname diverso)
dbuser_tracking = dbuser
dbpassword_tracking = dbpassword
dbhost_tracking = dbhost
dbport_tracking = dbport
dbname_tracking = "idumanager"

# main(in_out_tablelist, dbuser, dbpassword, dbhost, dbport, dbname, jobType, job_name, datasource_name = None, step_count = None, operation = None)


# Test execution
if __name__ == "__main__":
    main(in_out_tablelist, dbuser, dbpassword, dbhost, dbport, dbname, jobType, job_name, datasource_name, step_count, operation, dbuser_tracking, dbpassword_tracking, dbhost_tracking, dbport_tracking, dbname_tracking)
