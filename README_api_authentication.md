# IDU API Ingestion with Authentication Support

Questo documento spiega come utilizzare il programma `jc_api_endpoint_auth.py` per interagire con API che richiedono autenticazione.

## Metodi di Autenticazione Supportati

### 1. Nessuna Autenticazione (NO_AUTH)
Perfetto per API pubbliche che non richiedono credenziali.

```python
NO_AUTH_CONFIG = {
    "InOutTableList": [("https://jsonplaceholder.typicode.com/posts/1", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "JSONPlaceholder API",
    "auth_method": "NO_AUTH",
    "auth_credentials": None
}
```

### 2. API Key Authentication (API_KEY)
Utile per API che richiedono una chiave API negli headers.

```python
API_KEY_CONFIG = {
    "InOutTableList": [("https://api.example.com/data", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "Authenticated API",
    "auth_method": "API_KEY",
    "auth_credentials": json.dumps({
        "api_key": "your_actual_api_key_here",
        "header_name": "X-API-Key"  # o "Authorization", "api-key", ecc.
    })
}
```

**Esempi di API reali:**
- OpenWeatherMap: `X-API-Key` header
- GitHub API: `Authorization` header con `token <your_token>`
- Google APIs: `X-Goog-Api-Key` header

### 3. Bearer Token Authentication (BEARER)
Per API che utilizzano JWT tokens o access tokens.

```python
BEARER_CONFIG = {
    "InOutTableList": [("https://api.example.com/protected-data", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "Authenticated API",
    "auth_method": "BEARER",
    "auth_credentials": json.dumps({
        "token": "your_jwt_or_access_token_here"
    })
}
```

**Esempi di API reali:**
- Twitter API v2
- GitHub API con personal access tokens
- Strapi CMS APIs
- Auth0 APIs

### 4. Basic Authentication (BASIC)
Per API che richiedono username e password in formato HTTP Basic Auth.

```python
BASIC_AUTH_CONFIG = {
    "InOutTableList": [("https://secure-api.example.com/data", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "Authenticated API",
    "auth_method": "BASIC",
    "auth_credentials": json.dumps({
        "username": "your_username_here",
        "password": "your_password_here"
    })
}
```

**Esempi di API reali:**
- JIRA REST API
- Confluence API
- Elasticsearch APIs
- Kibana APIs

### 5. OAuth2 Full Support (OAUTH2)
**Il programma ora gestisce completamente OAuth2 con diversi grant types!**

#### Opzione 1: Token Pre-ottenuto
Per API protette da OAuth2 (se hai già il token):

```python
OAUTH2_TOKEN_CONFIG = {
    "InOutTableList": [("https://oauth2-api.example.com/data", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "OAuth2 API",
    "auth_method": "OAUTH2",
    "auth_credentials": json.dumps({
        "token": "your_oauth2_access_token_here"
    })
}
```

#### ⚡ Opzione 2: Client Credentials Grant
Ottieni automaticamente token OAuth2 con client credentials:

```python
OAUTH2_CLIENT_CREDENTIALS_CONFIG = {
    "InOutTableList": [("https://api.oauth2-server.com/protected-data", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "OAuth2 API",
    "auth_method": "OAUTH2",
    "auth_credentials": json.dumps({
        "grant_type": "CLIENT_CREDENTIALS",
        "token_url": "https://oauth2-server.com/oauth2/token",
        "credentials": {
            "client_id": "your_client_id",
            "client_secret": "your_client_secret",
            "scope": "read write profile"
        }
    })
}
```

#### 🔐 Opzione 3: Password Grant
Per API che supportano autenticazione con username/password:

```python
OAUTH2_PASSWORD_CONFIG = {
    "InOutTableList": [("https://api.oauth2-server.com/protected-data", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "OAuth2 API",
    "auth_method": "OAUTH2",
    "auth_credentials": json.dumps({
        "grant_type": "PASSWORD",
        "token_url": "https://oauth2-server.com/oauth2/token",
        "credentials": {
            "client_id": "your_client_id",
            "client_secret": "your_client_secret",
            "username": "your_end_user_username",
            "password": "your_end_user_password",
            "scope": "read write"
        }
    })
}
```

#### 🔄 Opzione 4: Authorization Code Grant
Per applicazioni che ricevono authorization code da OAuth2 callback:

```python
OAUTH2_AUTH_CODE_CONFIG = {
    "InOutTableList": [("https://api.oauth2-server.com/protected-data", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "OAuth2 API",
    "auth_method": "OAUTH2",
    "auth_credentials": json.dumps({
        "grant_type": "AUTHORIZATION_CODE",
        "token_url": "https://oauth2-server.com/oauth2/token",
        "credentials": {
            "client_id": "your_client_id",
            "client_secret": "your_client_secret",
            "authorization_code": "code_from_oauth_redirect",
            "redirect_uri": "https://your-app.com/oauth2/callback"
        }
    })
}
```

## Come Utilizzare

1. **Seleziona la configurazione desiderata** modificando la variabile `current_config`:

```python
current_config = BEARER_CONFIG  # oppure API_KEY_CONFIG, BASIC_AUTH_CONFIG, ecc.
```

2. **Inserisci le tue credenziali reali** nei campi `auth_credentials`

3. **Aggiorna l'endpoint API** nella lista `InOutTableList`

4. **Esegui il programma**:
```bash
python jc_api_endpoint_auth.py
```

## Struttura del Database

La tabella `public.api_response` è stata estesa per supportare l'autenticazione:

```sql
CREATE TABLE public.api_response (
    id SERIAL PRIMARY KEY,
    api_endpoint TEXT NOT NULL,
    auth_method TEXT DEFAULT 'NO_AUTH',        -- Metodo di autenticazione usato
    response_data JSONB,                       -- Dati della risposta
    response_headers JSONB,                    -- Headers della risposta HTTP
    status_code INTEGER,                       -- Codice di stato HTTP
    error_message TEXT,                        -- Messaggio di errore (se presente)
    fetched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Gestione degli Errori

Il programma gestisce diversi tipi di errori:

- **Errori di autenticazione**: Credenziali errate o token scaduto
- **Errori di rete**: Timeout, connessione rifiutata
- **Errori HTTP**: Status code 4xx/5xx
- **Errori di parsing**: JSON malformato

Tutti gli errori vengono registrati nella tabella per il tracking completo.

## Sicurezza

- **Non salvare mai credenziali in chiaro nel codice**
- Usa variabili d'ambiente per le credenziali sensibili
- Ruota regolarmente token e chiavi API
- Utilizza HTTPS per tutte le chiamate API

## Esempi Pratici

### Esempio con GitHub API (Bearer Token)
```python
BEARER_CONFIG = {
    "InOutTableList": [("https://api.github.com/user/repos", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "GitHub API",
    "auth_method": "BEARER",
    "auth_credentials": json.dumps({
        "token": "ghp_your_github_personal_access_token_here"
    })
}
```

### Esempio con REST API Protetta da API Key
```python
API_KEY_CONFIG = {
    "InOutTableList": [("https://api.covid19api.com/summary", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "COVID19 API",
    "auth_method": "API_KEY",
    "auth_credentials": json.dumps({
        "api_key": "your_covid19_api_key",
        "header_name": "X-Access-Token"
    })
}
```

## Note Importanti

- Il programma è retrocompatibile: puoi utilizzare configurazioni senza autenticazione esattamente come prima
- Tutti i metodi di autenticazione mantengono la stessa interfaccia di tracking del sistema IDU
- Gli errori vengono sempre salvati nel database per consentire il debugging
- Il timeout predefinito è di 30 secondi per le chiamate API
