import psycopg2
import psycopg2.extras
from datetime import timedelta, datetime

import pandas as pd

from sqlalchemy import create_engine
import xml.etree.ElementTree as ET



# Dizionario con le possibili operazioni di trasformazione
operationDict = {"0": "JOIN", "1": "FILTER", "2": "SPATIALJOIN"}

# Dizionario con le possibili tipologie di job
jobTypeDict = {"0": "INGESTION", "1": "TRANSFORMATION", "2": "LOAD"}

ingestionTrackingQuery = "insert into public.ingestion_jobs (datasource_name,job_name,input_table,output_table,tstamp) VALUES (%s, %s,%s,%s,%s) ON CONFLICT DO NOTHING;"

transformationTrackingQuery = "insert into public.transform_jobs (job_name,input_table,output_table,tstamp,step_count,operation) VALUES (%s, %s,%s,%s,%s, %s) ON CONFLICT DO NOTHING;"

loadTrackingQuery = "insert into public.load_jobs (job_name,staging_table,centraldb_table,tstamp) VALUES (%s, %s,%s,%s) ON CONFLICT DO NOTHING;"


def connectDb(user, password, host, port, dbname):
    """Funzione che effettua la connessione ad un database."""

    try:
        ocon = psycopg2.connect(
            user=user, password=password, host=host, port=port, dbname=dbname
        )
        print("Connessione Riuscita ")
        ocon.set_client_encoding("UTF8")
    except:
        print("Connessione non riuscita")
        raise
    else:
        return ocon


def tracking(ocon, trackingQuery, **kwargs):
    """Funzione che inserisce un recod nella tabella ingestion_jobs, transform_jobs o load_jobs del database idumanager."""

    parameterlist = []
    cur = ocon.cursor()

    for arg in kwargs.values():
        parameterlist.append(arg)

    # print(trackingQuery)
    # print(parameterlist)
    cur.execute(trackingQuery, parameterlist)
    cur.close()
    ocon.commit()


def insertResourceLastModified(conn, output_table, output_schema, schema_idu_mngr, lastModified=None):

    with conn.cursor() as curr:

        if lastModified is not None:
            strrr = (
                "INSERT INTO public.resource_last_modified (resource_name, last_modified, ckan_resource_id, is_on_central_db, is_on_staging_area, is_updated, ready_to_load) VALUES('"
                + output_schema
                + "."
                + output_table
                + "', '"
                + lastModified
                + "', null, false, true, true, true) ON CONFLICT ON CONSTRAINT resource_last_modified_pk DO UPDATE set last_modified = '"
                + lastModified
                + "';"
            )
        else:
            strrr = (
                "INSERT INTO public.resource_last_modified (resource_name, last_modified, ckan_resource_id, is_on_central_db, is_on_staging_area, is_updated, ready_to_load) VALUES('"
                + output_schema
                + "."
                + output_table
                + "', '"
                + str(datetime.now())
                + "', null, false, true, true, true) ON CONFLICT ON CONSTRAINT resource_last_modified_pk DO UPDATE set last_modified = '"
                + str(datetime.now())
                + "';"
            )
        curr.execute(strrr)
        conn.commit()


def main( in_out_tablelist, dbuser, dbpassword, dbhost, dbport, dbname, jobType, job_name, datasource_name=None, step_count=None, operation=None, ):
    """
    Funzione che effettua un controllo sugli input e in base ad essi chiama una delle tre funzioni che effettuano il tracking in base al tipo di job. Se stiamo effettuando un job di TRANSFORMATION, questa funzione bisogna richiamarla ad ogni step_count, specificando il tipo di operation.

    Parametri di input:

    in_out_tablelist: lista di tuple. ogni tupla deve avere due elementi. Il primo che indica il nome della tabella di input, il secondo che indica
                        il nome della tabella di output.
                        Esempio: in_out_tablelist = [("InTable4", "OutTable4"), ("InTable5", "OutTable5"), ("InTable6", "OutTable6")]
    dbuser: nome utente del db operativo (postgres)
    dbpassword: password associata all'utente del db operativo col quale bisogna connettersi (Idudb001!)
    dbhost: host del db operativo (idu-postgres-01.c84h5k4zmxdn.eu-west-1.rds.amazonaws.com)
    dbport: porta (5432)
    dbname: nome del db operativo (idumanager)
    jobType: tipologia di job (possibili valori: INGESTION, TRANSFORMATION, LOAD)
    job_name: nome del job custom
    datasource_name: nome della sorgente dalla quale si vogliono ingestionare dati nella staging area. Questo parametro di default Ã¨ impostato a None. Bisogna
                        utilizzarlo solo se il jobType Ã¨ di tipo INGESTION.
                        N.B.: il datasource_name deve corrispondere al nome di una sorgente configurata sull'IDU Manager
    step_count: intero che indica lo step di transformation. Questo parametro di default Ã¨ impostato a None. Bisogna utilizzarlo solo se il jobType Ã¨ di
                tipo TRANSFORMATION
    operation: indica il tipo di operazione di trasformazione (possibili valori: FILTER, JOIN, SPATIALJOIN). Questo parametro di default Ã¨ impostato a
                None. Bisogna utilizzarlo solo se il jobType Ã¨ di tipo TRANSFORMATION
    """

    funzione_ingestion()

    ocon = connectDb(dbuser, dbpassword, dbhost, dbport, dbname)
    tstamp = datetime.now()

    # Lista che conterrÃ  dizionari con coppie di input_table ed output_table
    TrackingTableList = (
        []
    )  # [{"input_table": InTable1, "output_table": OutTable1}, {"input_table": InTable2, "output_table": OutTable2}, {"input_table": InTable3, "output_table": OutTable3}]

    for el in in_out_tablelist:
        # Si creano tanti dizionari per ogni tupla presente nella lista di input
        TrackingTableList.append({"input_table": el[0], "output_table": el[1]})

    # jobType puÃ² avere solo i seguenti valori: INGESTION, TRANSFORMATION o LOAD
    if jobType == jobTypeDict["0"] and datasource_name != None:
        for el in TrackingTableList:
            tracking(
                ocon,
                ingestionTrackingQuery,
                datasource_name=datasource_name,
                job_name=job_name,
                inp=el["input_table"],
                out=el["output_table"],
                tstamp=tstamp,
            )

    # step_count deve essere un intero
    # operation puÃ² avere solo i seguenti valori: FILTER, JOIN o SPATIALJOIN
    elif (
        jobType == jobTypeDict["1"]
        and step_count != None
        and operation != None
        and operation in operationDict.values()
    ):
        for el in TrackingTableList:
            tracking(
                ocon,
                transformationTrackingQuery,
                job_name=job_name,
                inp=el["input_table"],
                out=el["output_table"],
                tstamp=tstamp,
                step_count=step_count,
                operation=operation,
            )

    elif jobType == jobTypeDict["2"]:
        for el in TrackingTableList:
            tracking(
                ocon,
                loadTrackingQuery,
                job_name=job_name,
                inp=el["input_table"],
                out=el["output_table"],
                tstamp=tstamp,
            )

    else:
        print("Errore nell'impostazione degli input")

    for el in in_out_tablelist:
        tableNameList = el[1].split(".")
        outputSchema = tableNameList[0]
        outputTableName = tableNameList[1]
        insertResourceLastModified(
            ocon,
            outputTableName,
            outputSchema,
            "public",
            lastModified=str(datetime.now()),
        )


def createSchema(schema, ocon, ocur):
    try:
        sql = "SELECT schema_name FROM information_schema.schemata where schema_name = '{}'".format(
            schema
        )
        print("sto qui")
        ocur.execute(sql)

        if ocur.rowcount == 0:
            try:
                ocur.execute("create schema IF NOT EXISTS {}".format(schema))
                # print("Creazione schema {}".format(schema))
                ocon.commit()
            except:
                print("Errore Creazione schema {}".format(schema))
                raise
        else:
            print("SCHEMA GIA PRESENTE!")
    except:
        ocur.close()
        raise
    ocon.commit()


def funzione_ingestion():

    inputdbhost = "idu-postgres-stg.cf7bx6ewgdue.eu-south-1.rds.amazonaws.com"
    inputdbport = "5432"
    inputusername = "postgres"
    inputpassword = "Idudb001!"
    inputdatabase = "stagingarea"

    outputdbhost = "idu-postgres-stg.cf7bx6ewgdue.eu-south-1.rds.amazonaws.com"
    outputdbport = "5432"
    outputusername = "postgres"
    outputpassword = "Idudb001!"
    outputdatabase = "stagingarea"

    ocon = connectDb(
        outputusername, outputpassword, outputdbhost, outputdbport, outputdatabase
    )
    cur = ocon.cursor()
    createSchema("contatti", ocon, cur)
    cur.close()

    # Connessione al database
    engine = create_engine(
        "postgresql+psycopg2://"
        + inputusername
        + ":"
        + inputpassword
        + "@"
        + inputdbhost
        + ":"
        + inputdbport
        + "/"
        + inputdatabase,
        echo=True,
    )

    # Query SQL per unire i dati da entrambe le tabelle
    query = """
       SELECT
           a1.username,
           min(case when a1.attrname = 'Nome' then a1.textvalue end) as nome,
           min(case when a1.attrname = 'Cognome' then a1.textvalue end) as cognome,
           min(case when a1.attrname = 'CodiceFiscale' then a1.textvalue end) as codice_fiscale,
           min(case when a1.attrname = 'Sesso' then a1.textvalue end) as sesso,
           min(case when a1.attrname = 'email' then a1.textvalue end) as email,
           min(case when a1.attrname = 'DataNascita' then a1.datevalue end) as data_nascita,
           min(case when a1.attrname = 'StatoEsteroNascita' then a1.textvalue end) as stato_estero_nascita,
           min(case when a1.attrname = 'ComuneNascita' then a1.textvalue end) as comune_nascita,
           a2.profilexml as profilexml  -- Aggiungi il campo profilexml
       FROM
           contatti.authuserprofilesearch a1
       LEFT JOIN
           contatti.authuserprofiles a2 ON a1.username = a2.username
       GROUP BY
           a1.username, a2.profilexml
    """

    # Esegui la query e converti i risultati in un DataFrame Pandas
    result = engine.execute(query)
    data = result.fetchall()
    df = pd.DataFrame(data, columns=result.keys())

    # Funzione per estrarre valori da XML
    def extract_from_xml(xml_string, tag_name):
        if xml_string is None:
            return None

        try:
            root = ET.fromstring(xml_string)
            for attribute in root.findall(".//attribute"):
                if attribute.get("name") == tag_name:
                    monotext = attribute.find("monotext")
                    if monotext is not None and monotext.text is not None:
                        return monotext.text
            # Se il tag non è presente, restituisci None o un valore di default
            return None  # oppure return "valore_di_default" se vuoi specificare un valore predefinito
        except ET.ParseError:
            # Se c'è un errore durante il parsing XML, restituisci None
            return None

    # Estrai i valori di cellulare e PEC da profilexml
    df["cellulare"] = df["profilexml"].apply(
        lambda x: extract_from_xml(x, "Cellulare") if x is not None else None
    )
    df["pec"] = df["profilexml"].apply(
        lambda x: extract_from_xml(x, "PEC") if x is not None else None
    )

    # Rimuovi la colonna profilexml se non più necessaria
    df.drop(columns=["profilexml"], inplace=True)

    # Salva il DataFrame nella tabella PostgreSQL
    df.to_sql(
        name="anagrafica_portale",
        schema="contatti",
        con=engine,
        index=False,
        if_exists="replace",
    )


"""Parametri di input"""

# Lista di tuple. ogni tuplaè composta da due elementi: il primo che indica il nome della tabella di input e il secondo il nome della tabella di output
InOutTableList = [("contatti.authuserprofilesearch", "contatti.anagrafica_portale")]

in_out_tablelist = InOutTableList
dbuser = "postgres"
dbpassword = "Idudb001!"
dbhost = "stage-idu-postgresql.czjrp3ja7e6q.eu-south-1.rds.amazonaws.com"
dbport = "5432"
dbname = "idumanager"
jobType = "INGESTION"  # INGESTION, TRANSFORMATION o LOAD
job_name = "job_custom_contatti"
datasource_name = "Contatti Portale Istituzionale"  # None di default. Serve solo per il tracking dei job di ingestion
step_count = None  # None di default. Un intero che indica lo step di transformazion (0 per il primo step, 1 per il secondo ecc.)
operation = None  # None di default. FILTER, JOIN o SPATIALJOIN

# main(in_out_tablelist, dbuser, dbpassword, dbhost, dbport, dbname, jobType, job_name, datasource_name = None, step_count = None, operation = None)


# Test
main(in_out_tablelist, dbuser, dbpassword, dbhost, dbport, dbname, jobType, job_name, datasource_name)

# main(in_out_tablelist, "postgres", "Idudb001!", "idu-postgres-01.c84h5k4zmxdn.eu-west-1.rds.amazonaws.com", "5432", "idumanager", "TRANSFORMATION", "job_fittizio", step_count = 1, operation = "JOIN")

# main(in_out_tablelist, "postgres", "Idudb001!", "idu-postgres-01.c84h5k4zmxdn.eu-west-1.rds.amazonaws.com", "5432", "idumanager", "LOAD", "job_fittizio")
