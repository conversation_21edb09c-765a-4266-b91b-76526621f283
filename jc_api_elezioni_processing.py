import psycopg2
import psycopg2.extras
import requests
from datetime import datetime
from pytz import timezone
import json

# Dizionario con le possibili operazioni di trasformazione
operationDict = {"0": "JOIN", "1": "FILTER", "2": "SPATIALJOIN"}

# Dizionario con le possibili tipologie di job
jobTypeDict = {"0": "INGESTION", "1": "TRANSFORMATION", "2": "LOAD"}

ingestionTrackingQuery = "insert into public.ingestion_jobs (datasource_name,job_name,input_table,output_table,tstamp) VALUES (%s, %s,%s,%s,%s) ON CONFLICT ON CONSTRAINT ingestion_jobs_pk DO UPDATE SET (datasource_name, job_name, tstamp) = (EXCLUDED.datasource_name, EXCLUDED.job_name, EXCLUDED.tstamp);"

transformationTrackingQuery = "insert into public.transform_jobs (job_name,input_table,output_table,tstamp,step_count,operation) VALUES (%s, %s,%s,%s,%s, %s) ON CONFLICT ON CONSTRAINT transform_jobs_pk DO UPDATE SET (job_name, tstamp) = (EXCLUDED.job_name, EXCLUDED.tstamp);"

loadTrackingQuery = "insert into public.load_jobs (job_name,staging_table,centraldb_table,tstamp) VALUES (%s, %s,%s,%s) ON CONFLICT ON CONSTRAINT load_jobs_pk DO UPDATE SET (job_name, tstamp) = (EXCLUDED.job_name, EXCLUDED.tstamp);"

def connectDb(user, password, host, port, dbname):
    """Funzione che effettua la connessione ad un database. """

    try:
        ocon = psycopg2.connect(user=user,
                                password=password,
                                host=host,
                                port=port,
                                dbname=dbname
                                )
        print("Connessione Riuscita ")
        ocon.set_client_encoding('LATIN1')
    except:
        print("Connessione non riuscita")
        raise
    else:
        return ocon


def tracking(ocon, trackingQuery, **kwargs):
    """Funzione che inserisce un recod nella tabella ingestion_jobs, transform_jobs o load_jobs del database idumanager. """

    parameterlist = []
    cur = ocon.cursor()

    for arg in kwargs.values():
        parameterlist.append(arg)

    # print(trackingQuery)
    # print(parameterlist)
    cur.execute(trackingQuery, parameterlist)
    cur.close()
    ocon.commit()


def insertResourceLastModified(conn, output_table, output_schema, schema_idu_mngr, lastModified = None):

    with conn.cursor() as curr:

        if lastModified is not None:
            strrr = "INSERT INTO public.resource_last_modified (resource_name, last_modified) VALUES('"+output_schema+"."+output_table+"', '"+lastModified+"') ON CONFLICT ON CONSTRAINT resource_last_modified_pk DO UPDATE set last_modified = '"+lastModified+"';"
        else:
            strrr = "INSERT INTO public.resource_last_modified (resource_name, last_modified) VALUES('"+output_schema+"."+output_table+"', '"+str(datetime.now().astimezone(timezone('Europe/Rome')))+"') ON CONFLICT ON CONSTRAINT resource_last_modified_pk DO UPDATE set last_modified = '"+str(datetime.now().astimezone(timezone('Europe/Rome')))+"';"
        curr.execute(strrr)
        conn.commit()

def createElezioniConsultazioniTable(conn):
    """Crea la tabella elezioni_consultazioni se non esiste."""
    create_table_query = """
    CREATE TABLE IF NOT EXISTS public.elezioni_consultazioni (
        id SERIAL PRIMARY KEY,
        anno_elezioni INTEGER NOT NULL,
        progr_elezioni INTEGER NOT NULL,
        descrizione_consultazione TEXT,
        stato TEXT,
        rilevazione TEXT,
        data_elezioni INTEGER,
        data_ballottaggio INTEGER,
        data_chiusura TIMESTAMP,
        utente TEXT,
        data_reg TIMESTAMP,
        ddata_elezioni TIMESTAMP,
        ddata_ballottaggio TIMESTAMP,
        elezione INTEGER NOT NULL,
        descrizione_elezione TEXT,
        processed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Indici per prestazioni
    CREATE INDEX IF NOT EXISTS idx_elezioni_consultazioni_anno ON public.elezioni_consultazioni(anno_elezioni);
    CREATE INDEX IF NOT EXISTS idx_elezioni_consultazioni_progr ON public.elezioni_consultazioni(progr_elezioni);
    CREATE INDEX IF NOT EXISTS idx_elezioni_consultazioni_elezione ON public.elezioni_consultazioni(elezione);
    """
    with conn.cursor() as cur:
        cur.execute(create_table_query)
        conn.commit()
    print("Tabella elezioni_consultazioni verificata/creata")

def processApiResponseData(conn, api_response_id):
    """Elabora i dati dalla tabella api_response e li inserisce in elezioni_consultazioni."""

    # Recupera i dati dalla api_response
    select_query = """
    SELECT response_data FROM public.api_response
    WHERE id = %s AND response_data IS NOT NULL AND error_message IS NULL;
    """

    with conn.cursor() as cur:
        cur.execute(select_query, (api_response_id,))
        result = cur.fetchone()

    if not result:
        print(f"Nessun dato valido trovato per api_response_id: {api_response_id}")
        return

    response_data = result[0]

    # Verifica che sia un dict
    if not isinstance(response_data, dict):
        print("I dati di risposta non sono in formato JSON valido")
        return

    # Estrai la lista delle consultazioni
    consultazioni_list = response_data.get('consultazioniPresentationList', [])
    if not consultazioni_list:
        print("Nessuna consultazione trovata nei dati")
        return

    print(f"Elaborazione di {len(consultazioni_list)} consultazioni")

    # Prepara l'inserimento
    insert_query = """
    INSERT INTO public.elezioni_consultazioni (
        anno_elezioni, progr_elezioni, descrizione_consultazione, stato, rilevazione,
        data_elezioni, data_ballottaggio, data_chiusura, utente, data_reg,
        ddata_elezioni, ddata_ballottaggio, elezione, descrizione_elezione
    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);
    """

    inserted_count = 0

    with conn.cursor() as cur:
        for consultazione in consultazioni_list:
            consultazioni_dto = consultazione.get('consultazioniDTO', {})
            elezioni_list = consultazione.get('elezioniDTOList', [])

            # Campi della consultazione
            anno_elezioni = consultazioni_dto.get('annoElezioni')
            progr_elezioni = consultazioni_dto.get('progrElezioni')
            descrizione_consultazione = consultazioni_dto.get('descrizione')
            stato = consultazioni_dto.get('stato')
            rilevazione = consultazioni_dto.get('rilevazione')
            data_elezioni = consultazioni_dto.get('dataElezioni')
            data_ballottaggio = consultazioni_dto.get('dataBallottaggio')
            data_chiusura = consultazioni_dto.get('dataChiusura')
            utente = consultazioni_dto.get('utente')
            data_reg = consultazioni_dto.get('dataReg')
            ddata_elezioni = consultazioni_dto.get('ddataElezioni')
            ddata_ballottaggio = consultazioni_dto.get('ddataBallottaggio')

            # Per ogni elezione nella consultazione
            for elezione_dto in elezioni_list:
                elezione = elezione_dto.get('elezione')
                descrizione_elezione = elezione_dto.get('descrizione')

                # Inserisci il record
                cur.execute(insert_query, (
                    anno_elezioni, progr_elezioni, descrizione_consultazione, stato, rilevazione,
                    data_elezioni, data_ballottaggio, data_chiusura, utente, data_reg,
                    ddata_elezioni, ddata_ballottaggio, elezione, descrizione_elezione
                ))
                inserted_count += 1

        conn.commit()

    print(f"Inseriti {inserted_count} record nella tabella elezioni_consultazioni")

def getLatestApiResponseId(conn, api_endpoint_pattern):
    """Recupera l'ID dell'ultimo record api_response valido per l'endpoint specificato."""

    select_query = """
    SELECT id FROM public.api_response
    WHERE api_endpoint LIKE %s AND response_data IS NOT NULL AND error_message IS NULL
    ORDER BY fetched_at DESC
    LIMIT 1;
    """

    with conn.cursor() as cur:
        cur.execute(select_query, (api_endpoint_pattern,))
        result = cur.fetchone()

    if result:
        return result[0]
    else:
        return None

def main(in_out_tablelist, dbuser, dbpassword, dbhost, dbport, dbname, jobType, job_name,
         datasource_name=None, step_count=None, operation=None, auth_method="NO_AUTH",
         auth_credentials=None, dbuser_tracking=None, dbpassword_tracking=None, dbhost_tracking=None, dbport_tracking=None, dbname_tracking=None):
    """
    Funzione principale per elaborare i dati delle elezioni dalle API response.
    """

    ocon = connectDb(dbuser, dbpassword, dbhost, dbport, dbname)
    ocon_tracking = None
    if dbuser_tracking and dbpassword_tracking and dbhost_tracking and dbport_tracking and dbname_tracking:
        ocon_tracking = connectDb(dbuser_tracking, dbpassword_tracking, dbhost_tracking, dbport_tracking, dbname_tracking)
    else:
        ocon_tracking = ocon  # fallback to same db
    tstamp = datetime.now()

    # Crea la tabella di destinazione se necessario
    createElezioniConsultazioniTable(ocon)

    # Per job di TRANSFORMATION, elabora i dati
    if jobType == jobTypeDict["1"] and operation == "FILTER":  # Usiamo FILTER come marker per processing
        # Trova l'ultimo record api_response valido per le elezioni
        api_response_id = getLatestApiResponseId(ocon, '%elezioni%get-consultazioni%')

        if api_response_id:
            print(f"Elaborazione dati da api_response ID: {api_response_id}")
            processApiResponseData(ocon, api_response_id)

            # Tracking della trasformazione
            for el in in_out_tablelist:
                tracking(ocon_tracking, transformationTrackingQuery, job_name=job_name, inp=el[0], out=el[1], tstamp=tstamp, step_count=step_count, operation=operation)
        else:
            print("Nessun record api_response valido trovato per l'elaborazione")

    # Aggiorna il timestamp della risorsa
    for el in in_out_tablelist:
        tableNameList = el[1].split(".")
        outputSchema = tableNameList[0]
        outputTableName = tableNameList[1]
        insertResourceLastModified(ocon_tracking, outputTableName, outputSchema, "public", lastModified=str(datetime.now().astimezone(timezone('Europe/Rome'))))


"""Configurazione per il processing dei dati delle elezioni"""

PROCESSING_CONFIG = {
    "InOutTableList": [("public.api_response", "public.elezioni_consultazioni")],
    "jobType": "TRANSFORMATION",
    "job_name": "jc_api_elezioni_processing",
    "step_count": 1,
    "operation": "FILTER"  # Usato come marker per processing
}

current_config = PROCESSING_CONFIG

in_out_tablelist = current_config["InOutTableList"]
# dbuser = "postgres"
# dbpassword = "Idudb002!"
dbuser = "microuser"
dbpassword = "Idumicr021!"
dbhost = "prod-idu-postgresql.cwoe6jwphrnc.eu-south-1.rds.amazonaws.com"
dbport = "5432"
dbname = "stagingarea"
jobType = current_config["jobType"]
job_name = current_config["job_name"]
datasource_name = None
auth_method = "NO_AUTH"
auth_credentials = None
step_count = current_config["step_count"]
operation = current_config["operation"]

# Parametri per il database di tracking
dbuser_tracking = dbuser
dbpassword_tracking = dbpassword
dbhost_tracking = dbhost
dbport_tracking = dbport
dbname_tracking = "idumanager"

# Esecuzione del job
if __name__ == "__main__":
    print("Avvio processing dati elezioni")
    main(in_out_tablelist, dbuser, dbpassword, dbhost, dbport, dbname, jobType, job_name,
         datasource_name, step_count, operation, auth_method, auth_credentials, dbuser_tracking, dbpassword_tracking, dbhost_tracking, dbport_tracking, dbname_tracking)
