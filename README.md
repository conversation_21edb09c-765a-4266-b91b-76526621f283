# Utilizzo dei Servizi Web come sorgenti ETL tramite 'job custom'

Questo progetto contiene una evoluzione di script Python per l'estrazione, la trasformazione e il caricamento (ETL) di dati da diverse fonti, inclusi database e API web.

## Struttura del Progetto

Il progetto è composto dai seguenti script:

### 1. `job-template_ORIGINALE.py`

Questo file è il template di base per la creazione di job ETL. Definisce le funzioni essenziali per:

- Connettersi al database PostgreSQL.
- Tracciare l'esecuzione dei job (INGESTION, TRANSFORMATION, LOAD) in un database di `idumanager`.
- Aggiornare un timestamp di "ultima modifica" per le risorse elaborate.

Il template è progettato per essere esteso e personalizzato per specifici casi d'uso.

### 2. `job-template.py`

Una versione leggermente rivista e aggiornata del template originale. Mantiene la stessa logica di base, ma include piccole correzioni o miglioramenti.

### 3. `job_custom_anagrafica_portale_istituzionale_staging.py`

Questo è un job di "INGESTION" personalizzato che:

- Si connette al database di staging.
- Esegue una query SQL per unire le tabelle `authuserprofilesearch` e `authuserprofiles`.
- Estrae dati da un campo XML (`profilexml`) utilizzando `pandas`.
- Salva i dati trasformati nella tabella `anagrafica_portale`.

Questo script dimostra come utilizzare il template di base per un'attività ETL specifica.

### 4. `jc_api_endpoint.py`

Questo script introduce la capacità di estrarre dati da API web generiche. Le sue funzionalità principali includono:

- Utilizzo della libreria `requests` per effettuare chiamate HTTP GET.
- Una funzione `ingestFromApi` che salva la risposta JSON da un endpoint in una tabella `api_response` nel database di staging.
- Integrazione con il sistema di tracking dei job esistente.

### 5. `jc_api_endpoint_auth.py`

Questo script estende `jc_api_endpoint.py` con un robusto sistema di autenticazione per le API. Supporta diversi metodi:

- **Nessuna autenticazione**
- **API Key**
- **Bearer Token**
- **Basic Auth**
- **OAuth2** (con grant types Client Credentials, Authorization Code, e Password)

La tabella `api_response` è stata ampliata per includere dettagli sull'autenticazione e la gestione degli errori.

### 6. `jc_api_elezioni.py`

Questo è un esempio concreto di come utilizzare `jc_api_endpoint_auth.py`. Lo script è configurato per:

- Connettersi all'API delle elezioni del Comune di Cagliari.
- Utilizzare il grant type **OAuth2 Client Credentials** per l'autenticazione.
- Eseguire una chiamata per ottenere i dati delle consultazioni elettorali.
- Salvare i dati nella tabella `api_response`.

Questo script serve come un caso d'uso pratico per l'ingestion di dati da un'API sicura.
