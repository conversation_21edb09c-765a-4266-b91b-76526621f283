import psycopg2
import psycopg2.extras
from datetime import datetime
from pytz import timezone

#Dizionario con le possibili operazioni di trasformazione
operationDict = {"0": "JOIN", "1": "FILTER", "2": "SPATIALJOIN"}

#Dizionario con le possibili tipologie di job
jobTypeDict = {"0": "INGESTION", "1": "TRANSFORMATION", "2": "LOAD"}

ingestionTrackingQuery = "insert into public.ingestion_jobs (datasource_name,job_name,input_table,output_table,tstamp) VALUES (%s, %s,%s,%s,%s) ON CONFLICT ON CONSTRAINT ingestion_jobs_pk DO UPDATE SET (datasource_name, job_name, tstamp) = (EXCLUDED.datasource_name, EXCLUDED.job_name, EXCLUDED.tstamp);"

transformationTrackingQuery = "insert into public.transform_jobs (job_name,input_table,output_table,tstamp,step_count,operation) VALUES (%s, %s,%s,%s,%s, %s) ON CONFLICT ON CONSTRAINT transform_jobs_pk DO UPDATE SET (job_name, tstamp) = (EXCLUDED.job_name, EXCLUDED.tstamp);"

loadTrackingQuery = "insert into public.load_jobs (job_name,staging_table,centraldb_table,tstamp) VALUES (%s, %s,%s,%s) ON CONFLICT ON CONSTRAINT load_jobs_pk DO UPDATE SET (job_name, tstamp) = (EXCLUDED.job_name, EXCLUDED.tstamp);"

def connectDb(user, password, host, port, dbname):
    """Funzione che effettua la connessione ad un database. """
    
    try:
        ocon = psycopg2.connect(user=user,
                                password=password,
                                host=host,
                                port=port,
                                dbname=dbname
                                )
        print("Connessione Riuscita ")
        ocon.set_client_encoding('LATIN1')
    except:
        print("Connessione non riuscita")
        raise
    else:
        return ocon   
    
    
def tracking(ocon, trackingQuery, **kwargs):
    """Funzione che inserisce un recod nella tabella ingestion_jobs, transform_jobs o load_jobs del database idumanager. """
    
    parameterlist = []
    cur = ocon.cursor()
  
    for arg in kwargs.values():
        parameterlist.append(arg)
    
    #print(trackingQuery)
    #print(parameterlist)
    cur.execute(trackingQuery, parameterlist)
    cur.close()
    ocon.commit()     
    
def insertResourceLastModified(conn, output_table, output_schema, schema_idu_mngr, lastModified = None):

    with conn.cursor() as curr:

        if lastModified is not None:
            strrr = "INSERT INTO public.resource_last_modified (resource_name, last_modified) VALUES('"+output_schema+"."+output_table+"', '"+lastModified+"') ON CONFLICT ON CONSTRAINT resource_last_modified_pk DO UPDATE set last_modified = '"+lastModified+"';"
        else:
            strrr = "INSERT INTO public.resource_last_modified (resource_name, last_modified) VALUES('"+output_schema+"."+output_table+"', '"+str(datetime.now().astimezone(timezone('Europe/Rome')))+"') ON CONFLICT ON CONSTRAINT resource_last_modified_pk DO UPDATE set last_modified = '"+str(datetime.now().astimezone(timezone('Europe/Rome')))+"';"
        curr.execute(strrr)
        conn.commit()    
    
def main(in_out_tablelist, dbuser, dbpassword, dbhost, dbport, dbname, jobType, job_name, datasource_name = None, step_count = None, operation = None):
    """
    Funzione che effettua un controllo sugli input e in base ad essi chiama una delle tre funzioni che effettuano il tracking in base al tipo di job. Se stiamo effettuando un job di TRANSFORMATION, questa funzione bisogna richiamarla ad ogni step_count, specificando il tipo di operation.
    
    Parametri di input:
    
    in_out_tablelist: lista di tuple. ogni tupla deve avere due elementi. Il primo che indica il nome della tabella di input, il secondo che indica 
                        il nome della tabella di output.
                        Esempio: in_out_tablelist = [("InTable4", "OutTable4"), ("InTable5", "OutTable5"), ("InTable6", "OutTable6")]
    dbuser: nome utente del db operativo (postgres)
    dbpassword: password associata all'utente del db operativo col quale bisogna connettersi (Idudb001!)
    dbhost: host del db operativo (idu-postgres-01.c84h5k4zmxdn.eu-west-1.rds.amazonaws.com)
    dbport: porta (5432)
    dbname: nome del db operativo (idumanager)
    jobType: tipologia di job (possibili valori: INGESTION, TRANSFORMATION, LOAD)
    job_name: nome del job custom 
    datasource_name: nome della sorgente dalla quale si vogliono ingestionare dati nella staging area. Questo parametro di default Ã¨ impostato a None. Bisogna
                        utilizzarlo solo se il jobType Ã¨ di tipo INGESTION. 
                        N.B.: il datasource_name deve corrispondere al nome di una sorgente configurata sull'IDU Manager
    step_count: intero che indica lo step di transformation. Questo parametro di default Ã¨ impostato a None. Bisogna utilizzarlo solo se il jobType Ã¨ di
                tipo TRANSFORMATION
    operation: indica il tipo di operazione di trasformazione (possibili valori: FILTER, JOIN, SPATIALJOIN). Questo parametro di default Ã¨ impostato a
                None. Bisogna utilizzarlo solo se il jobType Ã¨ di tipo TRANSFORMATION
    """
    
    ocon = connectDb(dbuser, dbpassword, dbhost, dbport, dbname)
    tstamp = datetime.now()
 
    #Lista che conterrÃ  dizionari con coppie di input_table ed output_table
    TrackingTableList = [] #[{"input_table": InTable1, "output_table": OutTable1}, {"input_table": InTable2, "output_table": OutTable2}, {"input_table": InTable3, "output_table": OutTable3}]

    for el in in_out_tablelist:  
        #Si creano tanti dizionari per ogni tupla presente nella lista di input
        TrackingTableList.append({"input_table": el[0], "output_table": el[1]})       
 
    #jobType puÃ² avere solo i seguenti valori: INGESTION, TRANSFORMATION o LOAD
    if jobType == jobTypeDict["0"] and datasource_name != None:
        for el in TrackingTableList:
            tracking(ocon, ingestionTrackingQuery, datasource_name = datasource_name, job_name = job_name, inp = el["input_table"], out = el["output_table"], tstamp = tstamp)
        
    #step_count deve essere un intero
    #operation puÃ² avere solo i seguenti valori: FILTER, JOIN o SPATIALJOIN
    elif jobType == jobTypeDict["1"] and step_count != None and operation != None and operation in operationDict.values():
        for el in TrackingTableList:
            tracking(ocon, transformationTrackingQuery, job_name = job_name, inp = el["input_table"], out = el["output_table"], tstamp = tstamp, step_count = step_count, operation = operation)
        
    elif jobType == jobTypeDict["2"]:
        for el in TrackingTableList:
            tracking(ocon, loadTrackingQuery, job_name = job_name, inp = el["input_table"], out = el["output_table"], tstamp = tstamp)
        
    else:
        print("Errore nell'impostazione degli input")
        
    for el in in_out_tablelist:  
        tableNameList = el[1].split(".")
        outputSchema = tableNameList[0]
        outputTableName = tableNameList[1]
        insertResourceLastModified(ocon, outputTableName, outputSchema, "public", lastModified = str(datetime.now().astimezone(timezone('Europe/Rome'))))


"""Parametri di input"""

#Lista di tuple. ogni tupla Ã¨ composta da due elementi: il primo che indica il nome della tabella di input e il secondo il nome della tabella di output
InOutTableList = [("InTable4", "OutTable4"), ("InTable5", "OutTable5"), ("InTable6", "OutTable6")]

in_out_tablelist = InOutTableList
dbuser = "postgres"
dbpassword = "Idudb001!" 
dbhost = "idu-postgres-stg.czjrp3ja7e6q.eu-south-1.rds.amazonaws.com"
dbport = "5432"
dbname = "idumanager"
jobType = "INGESTION"  #INGESTION, TRANSFORMATION o LOAD
job_name = "job_fittizio"
datasource_name = None #None di default. Serve solo per il tracking dei job di ingestion
step_count = None #None di default. Un intero che indica lo step di transformazion (0 per il primo step, 1 per il secondo ecc.)
operation = None #None di default. FILTER, JOIN o SPATIALJOIN

#main(in_out_tablelist, dbuser, dbpassword, dbhost, dbport, dbname, jobType, job_name, datasource_name = None, step_count = None, operation = None)



#Test
#main(in_out_tablelist, "postgres", "Idudb001!", "idu-postgres-01.c84h5k4zmxdn.eu-west-1.rds.amazonaws.com", "5432", "idumanager", "INGESTION", "job_fittizio", datasource_name = "datasource_fittizio")

#main(in_out_tablelist, "postgres", "Idudb001!", "idu-postgres-01.c84h5k4zmxdn.eu-west-1.rds.amazonaws.com", "5432", "idumanager", "TRANSFORMATION", "job_fittizio", step_count = 1, operation = "JOIN")

#main(in_out_tablelist, "postgres", "Idudb001!", "idu-postgres-01.c84h5k4zmxdn.eu-west-1.rds.amazonaws.com", "5432", "idumanager", "LOAD", "job_fittizio")
    