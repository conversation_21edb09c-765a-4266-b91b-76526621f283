import psycopg2
import psycopg2.extras
import requests
from requests.auth import HTTPBasicAuth
from datetime import datetime
from pytz import timezone
import json

# Dizionario con le possibili operazioni di trasformazione
operationDict = {"0": "JOIN", "1": "FILTER", "2": "SPATIALJOIN"}

# Dizionario con le possibili tipologie di job
jobTypeDict = {"0": "INGESTION", "1": "TRANSFORMATION", "2": "LOAD"}

# Dizionario con i metodi di autenticazione supportati
authMethodsDict = {
    "NO_AUTH": "Nessuna autenticazione",
    "API_KEY": "API Key",
    "BEARER": "Bearer Token",
    "BASIC": "Basic Auth",
    "OAUTH2": "OAuth2 Token"
}

ingestionTrackingQuery = "insert into public.ingestion_jobs (datasource_name,job_name,input_table,output_table,tstamp) VALUES (%s, %s,%s,%s,%s) ON CONFLICT ON CONSTRAINT ingestion_jobs_pk DO UPDATE SET (datasource_name, job_name, tstamp) = (EXCLUDED.datasource_name, EXCLUDED.job_name, EXCLUDED.tstamp);"

transformationTrackingQuery = "insert into public.transform_jobs (job_name,input_table,output_table,tstamp,step_count,operation) VALUES (%s, %s,%s,%s,%s, %s) ON CONFLICT ON CONSTRAINT transform_jobs_pk DO UPDATE SET (job_name, tstamp) = (EXCLUDED.job_name, EXCLUDED.tstamp);"

loadTrackingQuery = "insert into public.load_jobs (job_name,staging_table,centraldb_table,tstamp) VALUES (%s, %s,%s,%s) ON CONFLICT ON CONSTRAINT load_jobs_pk DO UPDATE SET (job_name, tstamp) = (EXCLUDED.job_name, EXCLUDED.tstamp);"

def connectDb(user, password, host, port, dbname):
    """Funzione che effettua la connessione ad un database. """

    try:
        ocon = psycopg2.connect(user=user,
                                password=password,
                                host=host,
                                port=port,
                                dbname=dbname
                                )
        print("Connessione Riuscita ")
        ocon.set_client_encoding('LATIN1')
    except:
        print("Connessione non riuscita")
        raise
    else:
        return ocon


def tracking(ocon, trackingQuery, **kwargs):
    """Funzione che inserisce un recod nella tabella ingestion_jobs, transform_jobs o load_jobs del database idumanager. """

    parameterlist = []
    cur = ocon.cursor()

    for arg in kwargs.values():
        parameterlist.append(arg)

    # print(trackingQuery)
    # print(parameterlist)
    cur.execute(trackingQuery, parameterlist)
    cur.close()
    ocon.commit()


def insertResourceLastModified(conn, output_table, output_schema, schema_idu_mngr, lastModified = None):

    with conn.cursor() as curr:

        if lastModified is not None:
            strrr = "INSERT INTO public.resource_last_modified (resource_name, last_modified) VALUES('"+output_schema+"."+output_table+"', '"+lastModified+"') ON CONFLICT ON CONSTRAINT resource_last_modified_pk DO UPDATE set last_modified = '"+lastModified+"';"
        else:
            strrr = "INSERT INTO public.resource_last_modified (resource_name, last_modified) VALUES('"+output_schema+"."+output_table+"', '"+str(datetime.now().astimezone(timezone('Europe/Rome')))+"') ON CONFLICT ON CONSTRAINT resource_last_modified_pk DO UPDATE set last_modified = '"+str(datetime.now().astimezone(timezone('Europe/Rome')))+"';"
        curr.execute(strrr)
        conn.commit()

def createApiResponseTable(conn):
    """Crea la tabella api_response estesa con campi per l'autenticazione se non esiste."""
    create_table_query = """
    CREATE TABLE IF NOT EXISTS public.api_response (
        id SERIAL PRIMARY KEY,
        api_endpoint TEXT NOT NULL,
        auth_method TEXT DEFAULT 'NO_AUTH',
        response_data JSONB,
        response_headers JSONB,
        status_code INTEGER,
        error_message TEXT,
        fetched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

    -- Indice per prestazioni migliori
    CREATE INDEX IF NOT EXISTS idx_api_response_endpoint ON public.api_response(api_endpoint);
    CREATE INDEX IF NOT EXISTS idx_api_response_fetched_at ON public.api_response(fetched_at);
    """
    with conn.cursor() as cur:
        cur.execute(create_table_query)
        conn.commit()
    print("Tabella api_response estesa verificata/creata")

def getAuthHeaders(auth_method, auth_credentials):
    """Prepara gli headers di autenticazione basati sul metodo selezionato."""

    headers = {}
    auth = None

    try:
        if auth_method == "API_KEY":
            # Formato: {"api_key": "your_key", "header_name": "X-API-Key"}
            credentials = json.loads(auth_credentials) if isinstance(auth_credentials, str) else auth_credentials
            header_name = credentials.get("header_name", "X-API-Key")
            api_key = credentials.get("api_key", "")
            headers[header_name] = api_key
            print(f"Utilizzo API Key authentication con header: {header_name}")

        elif auth_method == "BEARER":
            # Formato: {"token": "your_bearer_token"}
            credentials = json.loads(auth_credentials) if isinstance(auth_credentials, str) else auth_credentials
            token = credentials.get("token", "")
            headers["Authorization"] = f"Bearer {token}"
            print("Utilizzo Bearer Token authentication")

        elif auth_method == "BASIC":
            # Formato: {"username": "user", "password": "pass"}
            credentials = json.loads(auth_credentials) if isinstance(auth_credentials, str) else auth_credentials
            username = credentials.get("username", "")
            password = credentials.get("password", "")
            auth = HTTPBasicAuth(username, password)
            print("Utilizzo Basic Authentication")

        elif auth_method == "OAUTH2":
            # Supporto completo OAuth2 con grant types
            credentials = json.loads(auth_credentials) if isinstance(auth_credentials, str) else auth_credentials

            # Se abbiamo un token pre-ottenuto, utilizzalo direttamente
            if "token" in credentials:
                token = credentials.get("token", "")
                headers["Authorization"] = f"Bearer {token}"
                print("Utilizzo OAuth2 Token esistente")
            else:
                # Altrimenti, ottieni un nuovo token con il grant type specificato
                grant_type = credentials.get("grant_type", "CLIENT_CREDENTIALS")
                token_url = credentials.get("token_url", "")
                token_credentials = credentials.get("credentials", {})

                if not token_url:
                    raise ValueError("token_url è obbligatorio per OAuth2 flow")

                token = getOAuth2TokenFull(grant_type, token_url, token_credentials)
                headers["Authorization"] = f"Bearer {token}"
                print(f"Utilizzo OAuth2 con grant type: {grant_type}")

        else:  # NO_AUTH
            print("Nessuna autenticazione richiesta")

        return headers, auth

    except (json.JSONDecodeError, KeyError) as e:
        print(f"Errore nel parsing delle credenziali di autenticazione: {e}")
        raise ValueError(f"Formato credenziali non valido per {auth_method}: {e}")

def ingestFromApi(api_url, conn, auth_method="NO_AUTH", auth_credentials=None):
    """Esegue la chiamata API con supporto per autenticazione e persiste la risposta nella tabella api_response."""

    try:
        print(f"Chiamata API: {api_url} (Auth: {auth_method})")

        # Prepara headers e autenticazione
        headers, auth = getAuthHeaders(auth_method, auth_credentials)

        # Headers di base
        if not headers:
            headers = {}

        # Aggiungi User-Agent per evitare problemi con alcuni API
        headers["User-Agent"] = "IDU-API-Ingestion/1.0"

        # Esegui la richiesta
        response = requests.get(api_url, headers=headers, auth=auth, timeout=30)
        response.raise_for_status()  # Solleva eccezione per status code di errore

        # Prepara i dati per l'inserimento
        response_headers = dict(response.headers)
        status_code = response.status_code
        error_message = None

        # Gestisci diversi tipi di risposta
        if response.headers.get('content-type', '').startswith('application/json'):
            response_data = response.json()
        else:
            try:
                response_data = response.json()
            except json.JSONDecodeError:
                response_data = {'text': response.text}

        # Debug: limita la dimensione dei dati per il print
        data_preview = str(response_data)[:500] + "..." if len(str(response_data)) > 500 else str(response_data)
        print(f"Risposta ricevuta (status: {status_code}): {data_preview}")

    except requests.RequestException as e:
        print(f"Errore nella chiamata API: {e}")
        # Salva anche gli errori nella tabella per tracking
        response_data = None
        response_headers = None
        status_code = getattr(e.response, 'status_code', None) if hasattr(e, 'response') and e.response else None
        error_message = str(e)

    except Exception as e:
        print(f"Errore generico durante la chiamata API: {e}")
        response_data = None
        response_headers = None
        status_code = None
        error_message = str(e)

    # Inserisci sempre nella tabella (anche in caso di errore per tracking)
    try:
        insert_query = """
        INSERT INTO public.api_response (api_endpoint, auth_method, response_data, response_headers, status_code, error_message)
        VALUES (%s, %s, %s, %s, %s, %s);
        """

        with conn.cursor() as cur:
            cur.execute(insert_query, (
                api_url,
                auth_method,
                psycopg2.extras.Json(response_data) if response_data else None,
                psycopg2.extras.Json(response_headers) if response_headers else None,
                status_code,
                error_message
            ))
            conn.commit()

        if error_message:
            print(f"Errore salvato nella tabella api_response: {error_message}")
        else:
            print(f"Risposta API salvata nella tabella api_response. Status: {status_code}")

    except Exception as e:
        print(f"Errore durante il salvataggio della risposta: {e}")
        raise

class OAuth2Manager:
    """Gestore completo per OAuth2 authentication con supporto per diversi grant types."""

    def __init__(self):
        self.token_cache = {}

    def get_client_credentials_token(self, token_url, client_id, client_secret, scope=None):
        """Ottieni token OAuth2 con Client Credentials Grant."""
        print("Richiesta token OAuth2 con Client Credentials...")

        data = {
            'grant_type': 'client_credentials',
            'client_id': client_id,
            'client_secret': client_secret
        }

        if scope:
            data['scope'] = scope

        try:
            response = requests.post(token_url, data=data, timeout=30)
            response.raise_for_status()

            token_data = response.json()
            access_token = token_data.get('access_token')
            expires_in = token_data.get('expires_in', 3600)  # Default 1 ora

            # Salva token in cache con timestamp di scadenza
            self.token_cache[token_url] = {
                'token': access_token,
                'expires_at': datetime.now().timestamp() + expires_in - 60  # -60 secondi per sicurezza
            }

            print("Token OAuth2 ottenuto con successo")
            return access_token

        except requests.RequestException as e:
            print(f"Errore nell'ottenimento del token OAuth2: {e}")
            raise

    def get_authorization_code_token(self, token_url, client_id, client_secret, auth_code, redirect_uri):
        """Ottieni token OAuth2 con Authorization Code Grant."""
        print("Richiesta token OAuth2 con Authorization Code...")

        data = {
            'grant_type': 'authorization_code',
            'client_id': client_id,
            'client_secret': client_secret,
            'code': auth_code,
            'redirect_uri': redirect_uri
        }

        try:
            response = requests.post(token_url, data=data, timeout=30)
            response.raise_for_status()

            token_data = response.json()
            access_token = token_data.get('access_token')

            print("Token OAuth2 ottenuto con successo")
            return access_token

        except requests.RequestException as e:
            print(f"Errore nell'ottenimento del token OAuth2: {e}")
            raise

    def get_password_token(self, token_url, client_id, client_secret, username, password, scope=None):
        """Ottieni token OAuth2 con Resource Owner Password Credentials Grant."""
        print("Richiesta token OAuth2 con Password Credentials...")

        data = {
            'grant_type': 'password',
            'client_id': client_id,
            'client_secret': client_secret,
            'username': username,
            'password': password
        }

        if scope:
            data['scope'] = scope

        try:
            response = requests.post(token_url, data=data, timeout=30)
            response.raise_for_status()

            token_data = response.json()
            access_token = token_data.get('access_token')
            refresh_token = token_data.get('refresh_token')

            print("Token OAuth2 ottenuto con successo")
            return access_token, refresh_token

        except requests.RequestException as e:
            print(f"Errore nell'ottenimento del token OAuth2: {e}")
            raise

    def refresh_token(self, token_url, client_id, client_secret, refresh_token):
        """Rinnova il token OAuth2 usando il refresh token."""
        print("Refresh del token OAuth2...")

        data = {
            'grant_type': 'refresh_token',
            'client_id': client_id,
            'client_secret': client_secret,
            'refresh_token': refresh_token
        }

        try:
            response = requests.post(token_url, data=data, timeout=30)
            response.raise_for_status()

            token_data = response.json()
            access_token = token_data.get('access_token')

            print("Token OAuth2 rinnovato con successo")
            return access_token

        except requests.RequestException as e:
            print(f"Errore nel refresh del token OAuth2: {e}")
            raise

    def get_token(self, token_url, client_id=None, client_secret=None, scope=None, username=None, password=None):
        """Ottieni token OAuth2, gestendo automaticamente refresh e cache."""

        # Controlla cache
        if token_url in self.token_cache:
            token_info = self.token_cache[token_url]
            if token_info['expires_at'] > datetime.now().timestamp():
                print("Utilizzo token OAuth2 dalla cache")
                return token_info['token']
            else:
                print("Token OAuth2 in cache scaduto")

        # Determina grant type basato sui parametri forniti
        if username and password:
            # Password Grant
            token, refresh = self.get_password_token(token_url, client_id, client_secret, username, password, scope)
            return token
        elif client_id and client_secret:
            # Client Credentials Grant
            token = self.get_client_credentials_token(token_url, client_id, client_secret, scope)
            return token
        else:
            raise ValueError("Parametri insufficienti per ottenere token OAuth2")

# Istanza globale OAuth2 manager
oauth2_manager = OAuth2Manager()

def getOAuth2TokenFull(grant_type, token_url, credentials):
    """
    Funzione principale per ottenere token OAuth2 con supporto completo per grant types.
    """
    global oauth2_manager

    try:
        if grant_type == "CLIENT_CREDENTIALS":
            client_id = credentials.get('client_id')
            client_secret = credentials.get('client_secret')
            scope = credentials.get('scope')

            token = oauth2_manager.get_client_credentials_token(token_url, client_id, client_secret, scope)
            return token

        elif grant_type == "AUTHORIZATION_CODE":
            client_id = credentials.get('client_id')
            client_secret = credentials.get('client_secret')
            auth_code = credentials.get('authorization_code')
            redirect_uri = credentials.get('redirect_uri')

            token = oauth2_manager.get_authorization_code_token(token_url, client_id, client_secret, auth_code, redirect_uri)
            return token

        elif grant_type == "PASSWORD":
            client_id = credentials.get('client_id')
            client_secret = credentials.get('client_secret')
            username = credentials.get('username')
            password = credentials.get('password')
            scope = credentials.get('scope')

            token, refresh = oauth2_manager.get_password_token(token_url, client_id, client_secret, username, password, scope)
            return token

        else:
            raise ValueError(f"Grant type OAuth2 non supportato: {grant_type}")

    except Exception as e:
        print(f"Errore OAuth2: {e}")
        raise

def getOAuth2Token(token_url, client_id, client_secret, scope=None):
    """Funzione helper per retrocompatibilità."""
    print("Utilizzo funzione OAuth2 semplificata")
    credentials = {
        'client_id': client_id,
        'client_secret': client_secret,
        'scope': scope
    }
    return getOAuth2TokenFull("CLIENT_CREDENTIALS", token_url, credentials)

def main(in_out_tablelist, dbuser, dbpassword, dbhost, dbport, dbname, jobType, job_name,
         datasource_name=None, step_count=None, operation=None, auth_method="NO_AUTH",
         auth_credentials=None, dbuser_tracking=None, dbpassword_tracking=None, dbhost_tracking=None, dbport_tracking=None, dbname_tracking=None):
    """
    Funzione estesa che supporta autenticazione API.

    Parametri aggiuntivi per autenticazione:
    auth_method: metodo di autenticazione (NO_AUTH, API_KEY, BEARER, BASIC, OAUTH2)
    auth_credentials: credenziali in formato JSON string o dict
                      - API_KEY: {"api_key": "your_key", "header_name": "X-API-Key"}
                      - BEARER: {"token": "bearer_token"}
                      - BASIC: {"username": "user", "password": "pass"}
                      - OAUTH2: {"token": "oauth2_token"}
    dbuser_tracking: nome utente del db di tracking (postgres)
    dbpassword_tracking: password associata all'utente del db di tracking
    dbhost_tracking: host del db di tracking
    dbport_tracking: porta del db di tracking (5432)
    dbname_tracking: nome del db di tracking (idumanager)
    """

    ocon = connectDb(dbuser, dbpassword, dbhost, dbport, dbname)
    ocon_tracking = None
    if dbuser_tracking and dbpassword_tracking and dbhost_tracking and dbport_tracking and dbname_tracking:
        ocon_tracking = connectDb(dbuser_tracking, dbpassword_tracking, dbhost_tracking, dbport_tracking, dbname_tracking)
    else:
        ocon_tracking = ocon  # fallback to same db
    tstamp = datetime.now()

    # Per job di INGESTION da API, esegui prima l'ingestion
    if jobType == jobTypeDict["0"] and datasource_name in ["JSONPlaceholder API", "Authenticated API", "OAuth2 API"]:
        # Crea la tabella estesa se necessario
        createApiResponseTable(ocon)

        # Esegui l'ingestion per ogni input (API endpoint)
        for input_table, _ in in_out_tablelist:
            if input_table.startswith("http"):
                ingestFromApi(input_table, ocon, auth_method, auth_credentials)

    # Lista che conterrà dizionari con coppie di input_table ed output_table
    TrackingTableList = [] # [{"input_table": InTable1, "output_table": OutTable1}, {"input_table": InTable2, "output_table": OutTable2}, {"input_table": InTable3, "output_table": OutTable3}]

    for el in in_out_tablelist:
        # Si creano tanti dizionari per ogni tupla presente nella lista di input
        TrackingTableList.append({"input_table": el[0], "output_table": el[1]})

    # jobType può avere solo i seguenti valori: INGESTION, TRANSFORMATION o LOAD
    if jobType == jobTypeDict["0"] and datasource_name != None:
        for el in TrackingTableList:
            tracking(ocon_tracking, ingestionTrackingQuery, datasource_name = datasource_name, job_name = job_name, inp = el["input_table"], out = el["output_table"], tstamp = tstamp)

    # step_count deve essere un intero
    # operation puÃ² avere solo i seguenti valori: FILTER, JOIN o SPATIALJOIN
    elif jobType == jobTypeDict["1"] and step_count != None and operation != None and operation in operationDict.values():
        for el in TrackingTableList:
            tracking(ocon_tracking, transformationTrackingQuery, job_name = job_name, inp = el["input_table"], out = el["output_table"], tstamp = tstamp, step_count = step_count, operation = operation)

    elif jobType == jobTypeDict["2"]:
        for el in TrackingTableList:
            tracking(ocon_tracking, loadTrackingQuery, job_name = job_name, inp = el["input_table"], out = el["output_table"], tstamp = tstamp)

    else:
        print("Errore nell'impostazione degli input")

    for el in in_out_tablelist:
        tableNameList = el[1].split(".")
        outputSchema = tableNameList[0]
        outputTableName = tableNameList[1]
        insertResourceLastModified(ocon_tracking, outputTableName, outputSchema, "public", lastModified = str(datetime.now().astimezone(timezone('Europe/Rome'))))


"""Parametri di input"""

# Esempi di configurazione per diversi tipi di autenticazione

# 1. API senza autenticazione (come prima)
NO_AUTH_CONFIG = {
    "InOutTableList": [("https://jsonplaceholder.typicode.com/posts/1", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "JSONPlaceholder API",
    "auth_method": "NO_AUTH",
    "auth_credentials": None
}

# 2. API con API Key
API_KEY_CONFIG = {
    "InOutTableList": [("https://api.example.com/data", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "Authenticated API",
    "auth_method": "API_KEY",
    "auth_credentials": json.dumps({
        "api_key": "your_api_key_here",
        "header_name": "X-API-Key"
    })
}

# 3. API con Bearer Token
BEARER_CONFIG = {
    "InOutTableList": [("https://api.example.com/data", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "Authenticated API",
    "auth_method": "BEARER",
    "auth_credentials": json.dumps({
        "token": "your_bearer_token_here"
    })
}

# 4. API con Basic Authentication
BASIC_AUTH_CONFIG = {
    "InOutTableList": [("https://api.example.com/data", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "Authenticated API",
    "auth_method": "BASIC",
    "auth_credentials": json.dumps({
        "username": "your_username",
        "password": "your_password"
    })
}

# 5. API con OAuth2 Client Credentials Grant
OAUTH2_CLIENT_CREDENTIALS_CONFIG = {
    "InOutTableList": [("https://api.oauth2-example.com/protected-data", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "OAuth2 API",
    "auth_method": "OAUTH2",
    "auth_credentials": json.dumps({
        "grant_type": "CLIENT_CREDENTIALS",
        "token_url": "https://oauth2-server.com/oauth2/token",
        "credentials": {
            "client_id": "your_client_id",
            "client_secret": "your_client_secret",
            "scope": "read write"
        }
    })
}

# 6. API con OAuth2 Authorization Code Grant
OAUTH2_AUTH_CODE_CONFIG = {
    "InOutTableList": [("https://api.oauth2-example.com/protected-data", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "OAuth2 API",
    "auth_method": "OAUTH2",
    "auth_credentials": json.dumps({
        "grant_type": "AUTHORIZATION_CODE",
        "token_url": "https://oauth2-server.com/oauth2/token",
        "credentials": {
            "client_id": "your_client_id",
            "client_secret": "your_client_secret",
            "authorization_code": "authorization_code_from_callback",
            "redirect_uri": "https://your-app.com/callback"
        }
    })
}

# 7. API con OAuth2 Password Grant
OAUTH2_PASSWORD_CONFIG = {
    "InOutTableList": [("https://api.oauth2-example.com/protected-data", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "OAuth2 API",
    "auth_method": "OAUTH2",
    "auth_credentials": json.dumps({
        "grant_type": "PASSWORD",
        "token_url": "https://oauth2-server.com/oauth2/token",
        "credentials": {
            "client_id": "your_client_id",
            "client_secret": "your_client_secret",
            "username": "user_username",
            "password": "user_password",
            "scope": "read write"
        }
    })
}

# 8. API con OAuth2 Token Pre-ottenuto
OAUTH2_TOKEN_CONFIG = {
    "InOutTableList": [("https://api.oauth2-example.com/protected-data", "public.api_response")],
    "jobType": "INGESTION",
    "datasource_name": "OAuth2 API",
    "auth_method": "OAUTH2",
    "auth_credentials": json.dumps({
        "token": "your_pre_obtained_oauth2_token"
    })
}

# Configurazione attualmente attiva (modifica per testare diversi metodi)
current_config = NO_AUTH_CONFIG  # oppure: API_KEY_CONFIG, BEARER_CONFIG, BASIC_AUTH_CONFIG, OAUTH2_CLIENT_CREDENTIALS_CONFIG, ecc.

in_out_tablelist = current_config["InOutTableList"]
dbuser = "postgres"
dbpassword = "Idudb001!"
dbhost = "stage-idu-postgresql.czjrp3ja7e6q.eu-south-1.rds.amazonaws.com"
dbport = "5432"
dbname = "stagingarea"
jobType = current_config["jobType"]
job_name = "jc_api_endpoint_auth"
datasource_name = current_config["datasource_name"]
auth_method = current_config["auth_method"]
auth_credentials = current_config["auth_credentials"]
step_count = None
operation = None

# Parametri per il database di tracking (stesso host, user, password, port del database di staging, solo dbname diverso)
dbuser_tracking = dbuser
dbpassword_tracking = dbpassword
dbhost_tracking = dbhost
dbport_tracking = dbport
dbname_tracking = "idumanager"

# Esecuzione del job
if __name__ == "__main__":
    print(f"Avvio job con autenticazione: {auth_method}")
    main(in_out_tablelist, dbuser, dbpassword, dbhost, dbport, dbname, jobType, job_name,
         datasource_name, step_count, operation, auth_method, auth_credentials, dbuser_tracking, dbpassword_tracking, dbhost_tracking, dbport_tracking, dbname_tracking)
